#include "lv_port_disp.h"
#include "lvgl.h"
//#include "main.h"
#include "st7789.h"



static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p);


/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/



void lv_port_disp_init(void)
{
    /*-----------------------------
     * Create a buffer for drawing
     *----------------------------*/

    /**
     * LVGL requires a buffer where it internally draws the widgets.
     * Later this buffer will passed to your display driver's `flush_cb` to copy its content to your display.
     * The buffer has to be greater than 1 display row
     *
     * There are 3 buffering configurations:
     * 1. Create ONE buffer:
     *      LVGL will draw the display's content here and writes it to your display
     *
     * 2. Create TWO buffer:
     *      LVGL will draw the display's content to a buffer and writes it your display.
     *      You should use DMA to write the buffer's content to the display.
     *      It will enable LVGL to draw the next part of the screen to the other buffer while
     *      the data is being sent form the first buffer. It makes rendering and flushing parallel.
     *
     * 3. Double buffering
     *      Set 2 screens sized buffers and set disp_drv.full_refresh = 1.
     *      This way LVGL will always provide the whole rendered screen in `flush_cb`
     *      and you only need to change the frame buffer's address.
     */

	/* 优化：使用双缓冲提高性能 */
	static lv_disp_draw_buf_t draw_buf_dsc;
    static lv_color_t buf_1[LCD_HOR_RES * 20];                        /*A buffer for 20 rows*/
    static lv_color_t buf_2[LCD_HOR_RES * 20];                        /*Another buffer for 20 rows*/
    lv_disp_draw_buf_init(&draw_buf_dsc, buf_1, buf_2, LCD_HOR_RES * 20);   /*Initialize the display buffer*/
	
	/*-----------------------------------
	* Register the display in LVGL
	*----------------------------------*/

	static lv_disp_drv_t disp_drv;  /*Descriptor of a display driver*/
	lv_disp_drv_init(&disp_drv);  /*Basic initialization*/

	/*Set up the functions to access to your display*/

	/*Set the resolution of the display*/
	disp_drv.hor_res = LCD_HOR_RES;
	disp_drv.ver_res = LCD_VER_RES;

	/*Used to copy the buffer's content to the display*/
	disp_drv.flush_cb = disp_flush;

	/*Set a display buffer*/
	disp_drv.draw_buf = &draw_buf_dsc;

	/* Fill a memory array with a color if you have GPU.
	* Note that, in lv_conf.h you can enable GPUs that has built-in support in LVGL.
	* But if you have a different GPU you can use with this callback.*/
	//disp_drv.gpu_fill_cb = gpu_fill;

	/*Finally register the driver*/
	lv_disp_drv_register(&disp_drv);
	st7789_fill_full(0x0000);
}

static lv_disp_drv_t * disp_drv_temp = NULL;
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
//	log("disp_flush: (%3d, %3d), (%3d, %3d)", area->x1, area->y1, area->x2, area->y2);
	
	//逐点写入
//	for(uint32_t y = area->y1; y <= area->y2; y++)
//	{
//		for(uint32_t x = area->x1; x <= area->x2; x++)
//		{
//			//LTDC_Buf[480-y+LCD_VER_RES*x]=color_p->full;
//			//ltdc_draw_pixel(x, y, color_p->full);
//			st7789_wr_point(x, y, color_p->full);
//			color_p++;
//		}
//	}
//	lv_disp_flush_ready(disp_drv);
	
	disp_drv_temp = disp_drv;
	
	//每次DMA实际会持续7ms，约17.1次=125ms刷一屏
	st7789_wr_aera(area->x1, area->y1, area->x2, area->y2, (uint16_t*)color_p);
    /*IMPORTANT!!!
     *Inform the graphics library that you are ready with the flushing*/
    //lv_disp_flush_ready(disp_drv);
	
}

void call_lv_disp_flush_ready(void)
{
	if (disp_drv_temp)
	{
		lv_disp_flush_ready(disp_drv_temp);
		disp_drv_temp = NULL;
	}
}
