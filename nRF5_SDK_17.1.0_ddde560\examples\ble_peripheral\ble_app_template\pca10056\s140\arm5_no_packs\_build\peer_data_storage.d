.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\ble\peer_manager\peer_data_storage.c
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\sdk_common.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdint.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdbool.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\string.h
.\_build\peer_data_storage.o: ..\config\sdk_config.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\compiler_abstraction.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\sdk_os.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stddef.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\system_nrf52840.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_bitfields.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf51_to_nrf52840.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52_to_nrf52840.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf52\nrf_mbr.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_svc.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\sdk_macros.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\ble\peer_manager\peer_data_storage.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gap.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_hci.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_ranges.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_types.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_err.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\ble\peer_manager\peer_manager_types.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\ble\common\ble_gatt_db.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_l2cap.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gatt.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gattc.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gatts.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_soc.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error_soc.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_nvic.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdio.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\ble\peer_manager\peer_manager_internal.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\ble\peer_manager\peer_id.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\fds\fds.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\log\nrf_log.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_internal.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_instance.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_types.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_ctrl.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_ctrl_internal.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_backend_interface.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.h
.\_build\peer_data_storage.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdlib.h
.\_build\peer_data_storage.o: ..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.h
