#include "protocol.h"
#include "sm_ready.h"
#include "storage.h"
#include "sm.h"
#include "algo.h"
#include "stdio.h"
#include "string.h"
#include "sm_measure.h"
#include "bm8563.h"
#include "ble_conn.h"

void protocol_ble_recv_data(nus_data_t* nus_data)
{
    //按键重载自动关机定时器
//    system_auto_power_off_timer_reload();


    NRF_LOG_INFO("nus_data->cmd = 0x%0.Xlen=%d", nus_data->payload.cmd,nus_data->payload.para_len);
//	NRF_LOG_INFO("%d %d %d %d %d",nus_data->payload.para[0],nus_data->payload.para[1],nus_data->payload.para[2],nus_data->payload.para[3],nus_data->payload.para[4]);
    switch (nus_data->payload.cmd) {
    case 0x05://APP控制设备改变测量模式
        if (nus_data->payload.para_len == 1) {
//            sm_ready_app_set_model(nus_data->payload.para[0]);
            protocol_ble_send_reply(0x06, 0);
        }
        break;
    case 0x08://询问设备加解锁状态
        if (nus_data->payload.para_len == 0) {
            uint32_t is_locked;
            storage_read_lock(&is_locked);
            nus_data_t data;
            data.payload.cmd = 0x09;	//指令号
            data.payload.para_len = 1;	//表示para的长度
            data.payload.para[0] = is_locked;	//错误
            data.payload_len = 2 + data.payload.para_len;
            frame_send_frame(&data);
        }
        break;

    case 0x0A://设置设备加解锁状态   平板同时下发时间戳（客户使用到期时间）
        if (nus_data->payload.para_len == 5) {
//            uint32_t is_locked = nus_data->payload.para[0];
			uint32_t is_locked = 0;
            uint32_t stamp = 0;
            nus_data_t data;
            data.payload.cmd = 0x0B;	//指令号

            if (!is_locked) {
//								sm_ready_single_mode();//单次模式
            }
            stamp = (nus_data->payload.para[1]<<24)|(nus_data->payload.para[2]<<16)|(nus_data->payload.para[3]<<8)|nus_data->payload.para[4];
//            storage_update_stamp(stamp);
//            //	nrf_delay_ms(200);
//            storage_update_lock(&is_locked);
            data.payload.cmd = 0x0B;	//指令号
            data.payload.para_len = 21;	//表示para的长度
            data.payload_len = 2 + data.payload.para_len;
//            storage_read_sn_str(&data.payload.para[1]);
            frame_send_frame(&data);
        }
        break;

    case 0x0C://读取设备的MAC地址
        if (nus_data->payload.para_len == 0) {
            ble_gap_addr_t addr;
            ret_code_t err_code = sd_ble_gap_addr_get(&addr);
            APP_ERROR_CHECK(err_code);

            nus_data_t data;
            data.payload.cmd = 0x0D;	//指令号
            data.payload.para_len = 6;	//表示para的长度
            data.payload.para[0] = addr.addr[0];
            data.payload.para[1] = addr.addr[1];
            data.payload.para[2] = addr.addr[2];
            data.payload.para[3] = addr.addr[3];
            data.payload.para[4] = addr.addr[4];
            data.payload.para[5] = addr.addr[5];
            data.payload_len = 2 + data.payload.para_len;
            frame_send_frame(&data);
        }
        break;

    case 0x0E://读取设备的序列号字符串
        if (nus_data->payload.para_len == 0) {
            nus_data_t data;
            data.payload.cmd = 0x0F;	//指令号
            data.payload.para_len = 20;	//表示para的长度
            storage_read_sn_str(data.payload.para);
            data.payload_len = 2 + data.payload.para_len;
            frame_send_frame(&data);
        }
        break;

    case 0x10://设置序列号字符串
        if (nus_data->payload.para_len == 20) {
            if (strlen((const char*)(nus_data->payload.para)) < 20) { //限制长度
                storage_update_sn_str(nus_data->payload.para);
                protocol_ble_send_reply(0x11, 0);
            } else {
                protocol_ble_send_reply(0x11, 1);
            }
        }
        break;

    case 0x12://读取设备的校准值
        if (nus_data->payload.para_len == 0) {
            cali_para_t cali_para;
            storage_read_cali_para(&cali_para);

            nus_data_t data;
            data.payload.cmd = 0x13;	//指令号
            data.payload.para_len = 29;	//表示para的长度  //old 25
            data.payload.para[0] = cali_para.is_valid;
            memcpy(&data.payload.para[1], &cali_para.d0, 28);  //old 24
            data.payload_len = 2 + data.payload.para_len;
            frame_send_frame(&data);
        }
        break;

    case 0x14://更改校准值
        if (nus_data->payload.para_len == 25) {
            cali_para_t cali_para;
            storage_read_cali_para(&cali_para);
            cali_para.is_valid = nus_data->payload.para[0];
            memcpy(&cali_para.d0, &nus_data->payload.para[1], 24);
            storage_update_cali_para(&cali_para);
            algo_init();
            protocol_ble_send_reply(0x15, 0);

            if (cali_para.is_valid == 0) {
//					sm_jump(SM_UNCALI, 0);
            } else {
                sm_jump(SM_READY, 0);
            }
        }
        break;

    case 0x16://执行一次校准
        if (nus_data->payload.para_len == 1) {
            if (nus_data->payload.para[0] == 2) {

//					sm_jump(SM_CALIBRATE, 2);
            } else if (nus_data->payload.para[0] == 3) {

//					sm_jump(SM_CALIBRATE, 3);
            }
        }
        break;

    case 0x18://读取设备的蓝牙名称字符串
        if (nus_data->payload.para_len == 0) {
            nus_data_t data;
            data.payload.cmd = 0x19;	//指令号
            data.payload.para_len = 20;	//表示para的长度
            memcpy(data.payload.para, ble_name_buf, 20);
            data.payload_len = 2 + data.payload.para_len;
            frame_send_frame(&data);
        }
        break;

    case 0x1A://读取设备的算法结果调整率
        if (nus_data->payload.para_len == 0) {
            cali_para_t cali_para;
            storage_read_cali_para(&cali_para);
            nus_data_t data;
            data.payload.cmd = 0x1B;	//指令号
            data.payload.para_len = 4;	//表示para的长度
            data.payload.para[0] = cali_para.result_ratio;
            data.payload.para[1] = 0;
            data.payload.para[2] = 0;
            data.payload.para[3] = 0;
            data.payload_len = 2 + data.payload.para_len;
            frame_send_frame(&data);
        }
        break;

    case 0x1C://设置设备的算法结果调整率
        if (nus_data->payload.para_len == 4) {
            cali_para_t cali_para;
            storage_read_cali_para(&cali_para);
            cali_para.result_ratio = *(uint32_t*)&nus_data->payload.para[0];
            storage_update_cali_para(&cali_para);
            algo_init();
            nus_data_t data;
            data.payload.cmd = 0x1D;	//指令号
            data.payload.para_len = 4;	//表示para的长度
            data.payload.para[0] = cali_para.result_ratio;
            data.payload.para[1] = 0;
            data.payload.para[2] = 0;
            data.payload.para[3] = 0;
            data.payload_len = 2 + data.payload.para_len;
            frame_send_frame(&data);
        }
        break;
    case 0x20://返回设备的版本信息
        if (nus_data->payload.para_len == 0) {

            char str2[50];
            snprintf(str2, sizeof(str2), "HW_V%s", HW_VER);
//            char str3[30];
//            snprintf(str3, sizeof(str3), "%s %s %s", FW_VER, __DATE__, __TIME__);
//            strcat(str2,str3);
            nus_data_t data;
            data.payload.cmd = 0x21;	//指令号
            data.payload.para_len = 20;	//表示para的长度
            data.payload_len = 2 + data.payload.para_len;
            for(int i =0; i<strlen(str2); i++) {
                data.payload.para[i] = str2[i];
            }
			NRF_LOG_INFO("frame_send_frame");
            frame_send_frame(&data);
			NRF_LOG_INFO("frame_send_frame over");
        }
        break;

    case 0x1E://可以测量成人皮肤
        if (nus_data->payload.para_len == 0) {
            test_aldult_on();
        }
        break;
    //divid ++++++++++++++++++++++++++++++++++++++++++++++++++++
    case  0x30: { //收到该命令后需要启动测量，并且返回结果
        uint32_t tmp;
        tmp = nus_data->payload.para[0];
        sm_measure_by_cmd(tmp);
        sm_jump(SM_MEASURE, nus_data->payload.para[0]);
    }
    break;
    case 0x32: { //收到该命令后需要更新k和b的值并启动一次校准，并将校准值上传
        uint32_t kb_statusval = 0x55;
        storage_update_kb_status(&kb_statusval);
        uint32_t ksign;
        ksign = nus_data->payload.para[0];
        storage_update_k_sign(&ksign);
        uint32_t kval;
        kval = (nus_data->payload.para[1]<<8)|nus_data->payload.para[2];
        storage_update_k(&kval);

        uint32_t bsign;
        bsign = nus_data->payload.para[3];
        storage_update_b_sign(&bsign);
        uint32_t bval;
        bval = (nus_data->payload.para[4]<<8)|nus_data->payload.para[5];
        storage_update_b(&bval);
        uint32_t tkval;
        storage_read_k(&tkval);
        uint32_t tbval;
        storage_read_b(&tbval);
//				NRF_LOG_INFO("\r\nkb===updata kval:%d bval:%d",kval,bval);
//				NRF_LOG_INFO("\r\nkb===read kval:%d bval:%d",kval,bval);
//				NRF_LOG_INFO("\r\nupdata k:%d b:%d",kval,bval);
        sm_measure_by_cmd(20); //切换到校准模式
        sm_jump(SM_READY, 0);

    }
    break;
    case  0x33: { //收到该命令后清除K B 和标志
        uint32_t kb_statusval = 0x00;
        storage_update_kb_status(&kb_statusval);
//				NRF_LOG_INFO("\r\nkb==Clear");
        //	sm_jump(SM_MEASURE, nus_data->payload.para[0]);
    }
    break;
    case  0x37: { //读取历史记录
        record_send_all_history();
    }
    break;
    case  0x38:   //删除历史记录
        delete_all_begin();
        break;
    case  0x3A: { //设置时间戳
        uint32_t stamp =0;
        stamp = (nus_data->payload.para[0]<<24)|(nus_data->payload.para[1]<<16)|(nus_data->payload.para[2]<<8)|nus_data->payload.para[3];
        if(SetTimeFromStamp(stamp,0,nus_data->payload.para[4])) {
            frame_send_string("stamp set OK");
        }
        stamp = 0xffffffff;
        storage_update_stamp(stamp);
        //NRF_LOG_INFO("Set RTC time");
    }
    break;
//		case  0x3B:   //设置时间戳
//			{
//				uint32_t stamp =0;
//				stamp = GetTimeStamp();
//			  snprintf(str, sizeof(str), "RTC=%d, stamp0, stamp1);
//				frame_send_string(str);
//				NRF_LOG_INFO("Read RTC time");
//			}
//			break;
    case 0x3C: { //读取预期时间戳
        char str[30];
        uint32_t stamp0,stamp1;
        stamp0 = GetTimeStamp();
        storage_read_stamp(&stamp1);
        snprintf(str, sizeof(str), "RTC=%td,Preset=%td", stamp0, stamp1);
        frame_send_string(str);
        NRF_LOG_INFO("RTC:%d perset:%d",stamp0,stamp1);
        NRF_LOG_PROCESS();

    }
    break;
    case 0x41: { //设置单位
        uint32_t deviceuint=0;
        deviceuint = nus_data->payload.para[0];
//        set_measure_unit(deviceuint);
        storage_updata_unit(&deviceuint);
        nus_data_t data;
        data.payload.cmd = 0x43;	//指令号
        data.payload.para_len = 1;	//表示para的长度
        storage_read_unit(&deviceuint);
        data.payload.para[0] = deviceuint;
        data.payload_len = 2 + data.payload.para_len;
        frame_send_frame(&data);
        NRF_LOG_INFO("set unit");
        NRF_LOG_PROCESS();
    }
    break;
    case 0x42: {
        uint32_t deviceuint=0;
        storage_read_unit(&deviceuint);
        nus_data_t data;
        data.payload.cmd = 0x43;	//指令号
        data.payload.para_len = 1;	//表示para的长度
        data.payload.para[0] = deviceuint;
        data.payload_len = 2 + data.payload.para_len;
        frame_send_frame(&data);
        NRF_LOG_INFO("read unit");
        NRF_LOG_PROCESS();
    }
    break;
    //end
    default:
        frame_send_string("CMD ERROR");
        break;
    }
}

//发送测量数据给APP
void protocol_ble_send_data(uint8_t error_state, uint8_t measure_valid,uint32_t channal,uint16_t dark, uint16_t green, uint16_t blue, uint16_t result, uint8_t total, uint8_t current)  //增加通道
{
    nus_data_t nus_data;
    uint8_t i = 0;
    nus_data.payload.cmd = 0x31;	//测量结果数据指令号
    nus_data.payload.para_len = 13;	//表示para的长度     divid  加了1个字节的通道
    nus_data.payload.para[i + 0] = error_state;	//错误代码
    nus_data.payload.para[i + 1] = measure_valid;	//测量手法有效（没有提前抬起）
    nus_data.payload.para[i + 2] = (uint8_t)channal;   //色板序号
    i = 1;
    nus_data.payload.para[i + 2] = dark >> 0;	//DARK LSB
    nus_data.payload.para[i + 3] = dark >> 8;	//DARK MSB
    nus_data.payload.para[i + 4] = green >> 0;	//GREEN LSB
    nus_data.payload.para[i + 5] = green >> 8;	//GREEN MSB
    nus_data.payload.para[i + 6] = blue >> 0;	//BLUE LSB
    nus_data.payload.para[i + 7] = blue >> 8;	//BLUE MSB
    nus_data.payload.para[i + 8] = result >> 0;	//结果值，单位mg/dL LSB
    nus_data.payload.para[i + 9] = result >> 8;	//结果值，单位mg/dL MSB
    nus_data.payload.para[i + 10] = total;	//当前模式，1表示单次测量，3表示三次平均测量,   实际为当前温度 高八位
    nus_data.payload.para[i + 11] = current;	//当前是本次测量中的第几次                    实际为当前温度 低八位
//	nus_data.payload.para[i + 10] = total;	//当前模式，1表示单次测量，3表示三次平均测量
//	nus_data.payload.para[i + 11] = current;	//当前是本次测量中的第几次
    nus_data.payload_len = 2 + nus_data.payload.para_len;
    frame_send_frame(&nus_data);
}
void protocol_ble_send_data_nochannal(uint8_t error_state, uint8_t measure_valid,uint16_t dark, uint16_t green, uint16_t blue, uint16_t result, uint8_t total, uint8_t current)
{
    nus_data_t nus_data;
    uint8_t i = 0;
    nus_data.payload.cmd = 0x01;	//测量结果数据指令号
    nus_data.payload.para_len = 12; //表示para的长度
    nus_data.payload.para[i + 0] = error_state; //错误代码
    nus_data.payload.para[i + 1] = measure_valid;	//测量手法有效（没有提前抬起）
    nus_data.payload.para[i + 2] = dark >> 0;	//DARK LSB
    nus_data.payload.para[i + 3] = dark >> 8;	//DARK MSB
    nus_data.payload.para[i + 4] = green >> 0;	//GREEN LSB
    nus_data.payload.para[i + 5] = green >> 8;	//GREEN MSB
    nus_data.payload.para[i + 6] = blue >> 0;	//BLUE LSB
    nus_data.payload.para[i + 7] = blue >> 8;	//BLUE MSB
    nus_data.payload.para[i + 8] = result >> 0; //结果值，单位mg/dL LSB
    nus_data.payload.para[i + 9] = result >> 8; //结果值，单位mg/dL MSB
    nus_data.payload.para[i + 10] = total;	//当前模式，1表示单次测量，3表示三次平均测量
    nus_data.payload.para[i + 11] = current;	//当前是本次测量中的第几次
    nus_data.payload_len = 2 + nus_data.payload.para_len;
    frame_send_frame(&nus_data);
}


//设备更新了测量模式，通知APP
void protocol_ble_send_model(uint8_t model)
{
    nus_data_t nus_data;

    nus_data.payload.cmd = 0x03;	//测量结果数据指令号
    nus_data.payload.para_len = 1;	//表示para的长度
    nus_data.payload.para[0] = model;	//当前模式
    nus_data.payload_len = 2 + nus_data.payload.para_len;
    frame_send_frame(&nus_data);
}

//完成了校准，将校准结果发送给APP
void protocol_ble_send_cali_data(cali_para_t *cali_para)
{
    nus_data_t nus_data;

    nus_data.payload.cmd = 0x17;	//测量结果数据指令号
    nus_data.payload.para_len = 25;	//表示para的长度
    nus_data.payload.para[0] = cali_para->is_valid;
    memcpy(&nus_data.payload.para[1], &(cali_para->d0), 24);
    nus_data.payload_len = 2 + nus_data.payload.para_len;
    frame_send_frame(&nus_data);
}

//回复确认
void protocol_ble_send_reply(uint8_t cmd, uint8_t error)
{
    nus_data_t nus_data;

    nus_data.payload.cmd = cmd;	//指令号
    nus_data.payload.para_len = 1;	//表示para的长度
    nus_data.payload.para[0] = error;	//错误
    nus_data.payload_len = 2 + nus_data.payload.para_len;
    frame_send_frame(&nus_data);
}


