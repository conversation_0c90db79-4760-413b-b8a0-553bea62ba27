#include "frame.h"
#include "protocol.h"
#include "nrf_queue.h"
#include "ble_nus.h"
#include "ble_conn.h"



//接收
static app_fifo_t m_ble_recv_fifo;//FIFO实例
static uint8_t m_ble_recv_buf[256] = {0};//FIFO数组
static bool m_recv_parsing = false;//正在解析接收到的数据的标志
//发送
NRF_QUEUE_DEF(nus_data_t, m_nus_send_queue, 5, NRF_QUEUE_MODE_NO_OVERFLOW);   //NUS发送数据队列
NRF_QUEUE_INTERFACE_DEC(nus_data_t, nus_send_queue);                            //NUS发送数据队列声明
NRF_QUEUE_INTERFACE_DEF(nus_data_t, nus_send_queue, &m_nus_send_queue);         //NUS发送数据队列定义


//发送字符串
void frame_send_string(char* str)
{
#if BLE_DEBUG
    ret_code_t ret_code;
    nus_data_t nus_data;

    if (strlen(str) > sizeof(nus_data.payload) - 1) {
        return;
    }

    if (ble_conn_is_connected()) {
        //组包
        nus_data_t nus_data;
        nus_data.payload.cmd = 0x07;	//测量结果数据指令号
        nus_data.payload.para_len = strlen(str);	//字符串长度
        strcpy((char*)&(nus_data.payload.para), str);//字符串
        nus_data.payload_len = 2 + nus_data.payload.para_len;
        //写入发送队列
        ret_code = nus_send_queue_push(&nus_data);
        APP_ERROR_CHECK(ret_code);
        //先执行一次底层发送
        ble_low_layer_send_data();
    }
#endif
}

//帧解析
static void frame_parse_recv_data(void)
{
    ret_code_t ret_code;
    m_recv_parsing = true;
    while (true) {
        //获取FIFO中的数据长度
        uint32_t bytes_in_fifo;
        ret_code = app_fifo_read(&m_ble_recv_fifo, NULL, &bytes_in_fifo);
        if (ret_code == NRF_ERROR_NOT_FOUND) { //FIFO空了
            break;
        } else if (ret_code != NRF_SUCCESS) {
            NRF_LOG_ERROR("FIFO read error: 0x%08X", ret_code);
            APP_ERROR_CHECK(ret_code);
        }

//        NRF_LOG_INFO("bytes_in_fifo = %d", bytes_in_fifo);
        if (bytes_in_fifo >= 2) { //一帧最少有2B
//            NRF_LOG_INFO("FIFO has enough data for frame header (>=2 bytes)");
            nus_data_t nus_data;

            //先看看FIFO中够不够一帧数据
            uint8_t len;
            ret_code = app_fifo_peek(&m_ble_recv_fifo, 1, &len);
            if (ret_code != NRF_SUCCESS) {
                NRF_LOG_ERROR("FIFO peek error: 0x%08X", ret_code);
                APP_ERROR_CHECK(ret_code);
            }

            if (bytes_in_fifo >= len + 2) { //可以读出完整的帧
                //读出frame
                uint32_t read_len = len + 2;
                ret_code = app_fifo_read(&m_ble_recv_fifo, (uint8_t*)&(nus_data.payload), &read_len);
                nus_data.payload_len = read_len;
                APP_ERROR_CHECK(ret_code);
                //调用上层解析
                protocol_ble_recv_data(&nus_data);
                if(len>120) {
                    frame_clear();
                }
            } else { //不够完整的一帧
                NRF_LOG_INFO("Incomplete frame: need %d bytes, have %d bytes", len + 2, bytes_in_fifo);
                break;
            }
        } else { //长度不够一帧
            NRF_LOG_INFO("Not enough data for frame header: have %d bytes, need 2", bytes_in_fifo);
            break;
        }
    }
	NRF_LOG_INFO("m_recv_parsing false");
    m_recv_parsing = false;
}


//NUS接收到数据
void frame_recv_ble_data(const uint8_t* p_data, uint16_t length)
{
    ret_code_t ret_code;
    static uint32_t  cnt = 0;

    //添加到FIFO准备处理
    //	app_fifo_flush(&m_ble_recv_fifo);  //divid add
    //	 NRF_LOG_INFO("S1L=%d d0=%d d1= %d d2 =%d", length,p_data[0],p_data[1],p_data[2]);
    ret_code = app_fifo_write(&m_ble_recv_fifo, p_data, (uint32_t*)&length);
    if (ret_code == NRF_ERROR_NO_MEM) {
        NRF_LOG_WARNING("fifo is full");
        app_fifo_flush(&m_ble_recv_fifo);//清空FIFO
    } else if (ret_code == NRF_SUCCESS) {
        NRF_LOG_INFO("FIFO write successful, written length=%d", length);
    } else {
        NRF_LOG_ERROR("FIFO write failed with error: 0x%08X", ret_code);
        APP_ERROR_CHECK(ret_code);
    }
    cnt = 0;
    //触发解析
//    NRF_LOG_INFO("About to call frame_parse_recv_data");
    while (m_recv_parsing == true) { //当正在解析时死等
        nrf_delay_ms(1);
        if(cnt++>1000) {
            NRF_LOG_WARNING("Timeout waiting for parsing to complete");
            break;
        }
    }
    frame_parse_recv_data();
}


//将队列里待发送的数据送往协议栈
void ble_low_layer_send_data(void)
{
    if (ble_conn_is_connected() == false) {
        NRF_LOG_WARNING("BLE not connected, cannot send data");
        return;
    }

    while (1) {
        ret_code_t ret_code;
        nus_data_t nus_data;
        //如果队列还有待发送数据
        if (nus_send_queue_peek(&nus_data) == NRF_SUCCESS) {
            //发送到协议栈队列
            ret_code = nus_send_byte((uint8_t*)&(nus_data.payload), nus_data.payload_len);
            if (ret_code == NRF_SUCCESS) { //发送成功
//                frame_send_string("Send successful\r\n");
                nus_send_queue_pop(&nus_data);
            } else if (ret_code == NRF_ERROR_RESOURCES) { //队列已满
                break;//退出不再写入队列
            } else if (ret_code == NRF_ERROR_INVALID_STATE) { //对端没有打开监听
                NRF_LOG_WARNING("nus_send_byte  NRF_ERROR_INVALID_STATE");
                break;//退出不再写入队列
            } else if (ret_code == BLE_ERROR_GATTS_SYS_ATTR_MISSING) { //正在连接还没完成
                break;//退出不再写入队列7
            } else {
                NRF_LOG_ERROR("ret_code = %d", ret_code);
                APP_ERROR_CHECK(ret_code);
            }
        } else {
            break;//没有待发送的数据了
        }
    }
}


//发送一帧数据
void frame_send_frame(nus_data_t* p_nus_data)
{
#if BLE_COMM
    ret_code_t ret_code;

    if (ble_conn_is_connected()) {
        //写入发送队列
        ret_code = nus_send_queue_push(p_nus_data);
        if (ret_code == NRF_SUCCESS) {
            //先执行一次底层发送
            ble_low_layer_send_data();
        } else {
            NRF_LOG_ERROR("nus_send_queue_push failed: 0x%08X", ret_code);
        }
    } else {
        NRF_LOG_WARNING("BLE not connected, cannot send frame");
    }
#endif
}


//清空FIFO
void frame_clear(void)
{
    nus_send_queue_reset();
}



//通信协议初始化
void frame_init(void)
{
    ret_code_t ret_code;

    //队列，用于缓存BLE接收的数据
    ret_code = app_fifo_init(&m_ble_recv_fifo, m_ble_recv_buf, sizeof(m_ble_recv_buf));
    APP_ERROR_CHECK(ret_code);
    app_fifo_flush(&m_ble_recv_fifo);
    APP_ERROR_CHECK(ret_code);
}


