/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"




void setup_scr_average(lv_ui *ui)
{
    //Write codes average
    ui->average = lv_obj_create(NULL);
    lv_obj_set_size(ui->average, 320, 170);
    lv_obj_set_scrollbar_mode(ui->average, LV_SCROLLBAR_MODE_OFF);

    //Write style for average, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->average, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->average, lv_color_hex(0x030303), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->average, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes average_img_1
    ui->average_img_1 = lv_img_create(ui->average);
    lv_obj_add_flag(ui->average_img_1, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->average_img_1, &_average_alpha_64x64);
    lv_img_set_pivot(ui->average_img_1, 50,50);
    lv_img_set_angle(ui->average_img_1, 0);
    lv_obj_set_pos(ui->average_img_1, 128, 39);
    lv_obj_set_size(ui->average_img_1, 64, 64);

    //Write style for average_img_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_img_recolor_opa(ui->average_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->average_img_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->average_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->average_img_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes average_label_1
    ui->average_label_1 = lv_label_create(ui->average);
    lv_label_set_text(ui->average_label_1, "已切换至平均模式");
    lv_label_set_long_mode(ui->average_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->average_label_1, 77, 110);
    lv_obj_set_size(ui->average_label_1, 165, 28);

    //Write style for average_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->average_label_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->average_label_1, &lv_font_Chinese_bold, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->average_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->average_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->average_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of average.


    //Update current screen layout.
    lv_obj_update_layout(ui->average);

}
