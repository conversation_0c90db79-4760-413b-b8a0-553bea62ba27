.\_build\tx1812mxa5_test.o: ..\device\TX1812MXA5_test.c
.\_build\tx1812mxa5_test.o: ..\device\TX1812MXA5_test.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\tx1812mxa5_test.o: ..\device\TX1812MXA5.h
.\_build\tx1812mxa5_test.o: ..\config\main.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\string.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdio.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\log\nrf_log.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\sdk_common.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\tx1812mxa5_test.o: ..\config\sdk_config.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\compiler_abstraction.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\sdk_os.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\system_nrf52840.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_bitfields.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf51_to_nrf52840.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52_to_nrf52840.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf52\nrf_mbr.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_svc.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\sdk_macros.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_internal.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_instance.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_types.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_ctrl.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_ctrl_internal.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_backend_interface.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdlib.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_soc.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error_soc.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_nvic.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_default_backends.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_err.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gap.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_hci.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_ranges.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_types.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_l2cap.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gatt.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gattc.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gatts.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\hal\nrf_gpio.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\integration\nrfx\nrfx_config.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_common.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf_peripherals.h
.\_build\tx1812mxa5_test.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_peripherals.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\integration\nrfx\nrfx_glue.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\integration\nrfx\legacy/apply_old_config.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs_nrf52840.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_coredep.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_atomic.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\util\sdk_resources.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_sd_def.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_errors.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\delay\nrf_delay.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\timer\app_timer.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.h
.\_build\tx1812mxa5_test.o: ..\..\..\..\..\..\components\libraries\fifo\app_fifo.h
