.\_build\power.o: ..\device\power.c
.\_build\power.o: ..\device\power.h
.\_build\power.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdint.h
.\_build\power.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\string.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\system_nrf52840.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_bitfields.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf51_to_nrf52840.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52_to_nrf52840.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\compiler_abstraction.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\power.o: ..\config\main.h
.\_build\power.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdio.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\log\nrf_log.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\sdk_common.h
.\_build\power.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdbool.h
.\_build\power.o: ..\config\sdk_config.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\sdk_os.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\power.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stddef.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf52\nrf_mbr.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_svc.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\sdk_macros.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_internal.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_instance.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_types.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_ctrl.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_ctrl_internal.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_backend_interface.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.h
.\_build\power.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdlib.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_soc.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error_soc.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_nvic.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_default_backends.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_err.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gap.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_hci.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_ranges.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_types.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_l2cap.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gatt.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gattc.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gatts.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\hal\nrf_gpio.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\power.o: ..\..\..\..\..\..\integration\nrfx\nrfx_config.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_common.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf_peripherals.h
.\_build\power.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_peripherals.h
.\_build\power.o: ..\..\..\..\..\..\integration\nrfx\nrfx_glue.h
.\_build\power.o: ..\..\..\..\..\..\integration\nrfx\legacy/apply_old_config.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs_nrf52840.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_coredep.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_atomic.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\util\sdk_resources.h
.\_build\power.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_sd_def.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_errors.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\delay\nrf_delay.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\timer\app_timer.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.h
.\_build\power.o: ..\..\..\..\..\..\components\libraries\fifo\app_fifo.h
.\_build\power.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_gpiote.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_gpiote.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_gpiote.h
.\_build\power.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_power.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_power.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_power.h
.\_build\power.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_power_clock.h
.\_build\power.o: ..\device\app_timer.h
.\_build\power.o: ..\gui\gui.h
.\_build\power.o: ..\sm\sm.h
.\_build\power.o: ..\lvgl\generated\gui_guider.h
.\_build\power.o: ..\lvgl\lvgl.h
.\_build\power.o: ..\lvgl\src/misc/lv_log.h
.\_build\power.o: ..\lvgl\src/misc/../lv_conf_internal.h
.\_build\power.o: ..\lvgl\lv_conf.h
.\_build\power.o: ..\lvgl\src/misc/../lv_conf_kconfig.h
.\_build\power.o: ..\lvgl\src/misc/lv_types.h
.\_build\power.o: ..\lvgl\src/misc/lv_timer.h
.\_build\power.o: ..\lvgl\src/misc/lv_math.h
.\_build\power.o: ..\lvgl\src/misc/lv_mem.h
.\_build\power.o: ..\lvgl\src/misc/lv_async.h
.\_build\power.o: ..\lvgl\src/misc/lv_anim_timeline.h
.\_build\power.o: ..\lvgl\src/misc/lv_anim.h
.\_build\power.o: ..\lvgl\src/misc/lv_printf.h
.\_build\power.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdarg.h
.\_build\power.o: ..\lvgl\src/hal/lv_hal.h
.\_build\power.o: ..\lvgl\src/hal/lv_hal_disp.h
.\_build\power.o: ..\lvgl\src/hal/lv_hal.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_draw.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/lv_style.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/../font/lv_font.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/../font/lv_symbol_def.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/../font/../misc/lv_area.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/lv_color.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/lv_assert.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/lv_txt.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/lv_bidi.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/lv_style_gen.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_img_decoder.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_img_buf.h
.\_build\power.o: ..\lvgl\src/hal/../draw/../misc/lv_fs.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_img_cache.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_draw_rect.h
.\_build\power.o: ..\lvgl\src/hal/../draw/sw/lv_draw_sw_gradient.h
.\_build\power.o: ..\lvgl\src/hal/../draw/sw/lv_draw_sw_dither.h
.\_build\power.o: ..\lvgl\src/hal/../draw/sw/../../core/lv_obj_pos.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_draw_label.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_draw_img.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_draw_line.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_draw_triangle.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_draw_arc.h
.\_build\power.o: ..\lvgl\src/hal/../draw/lv_draw_mask.h
.\_build\power.o: ..\lvgl\src/hal/../misc/lv_ll.h
.\_build\power.o: ..\lvgl\src/hal/lv_hal_indev.h
.\_build\power.o: ..\lvgl\src/hal/lv_hal_tick.h
.\_build\power.o: ..\lvgl\src/core/lv_obj.h
.\_build\power.o: ..\lvgl\src/core/lv_obj_tree.h
.\_build\power.o: ..\lvgl\src/core/lv_obj_scroll.h
.\_build\power.o: ..\lvgl\src/core/lv_obj_style.h
.\_build\power.o: ..\lvgl\src/core/lv_obj_style_gen.h
.\_build\power.o: ..\lvgl\src/core/lv_obj_draw.h
.\_build\power.o: ..\lvgl\src/core/lv_obj_class.h
.\_build\power.o: ..\lvgl\src/core/lv_event.h
.\_build\power.o: ..\lvgl\src/core/lv_group.h
.\_build\power.o: ..\lvgl\src/core/lv_indev.h
.\_build\power.o: ..\lvgl\src/core/lv_refr.h
.\_build\power.o: ..\lvgl\src/core/lv_disp.h
.\_build\power.o: ..\lvgl\src/core/lv_theme.h
.\_build\power.o: ..\lvgl\src/font/lv_font_loader.h
.\_build\power.o: ..\lvgl\src/font/lv_font_fmt_txt.h
.\_build\power.o: ..\lvgl\src/widgets/lv_arc.h
.\_build\power.o: ..\lvgl\src/widgets/lv_btn.h
.\_build\power.o: ..\lvgl\src/widgets/lv_img.h
.\_build\power.o: ..\lvgl\src/widgets/lv_label.h
.\_build\power.o: ..\lvgl\src/widgets/lv_line.h
.\_build\power.o: ..\lvgl\src/widgets/lv_table.h
.\_build\power.o: ..\lvgl\src/widgets/lv_checkbox.h
.\_build\power.o: ..\lvgl\src/widgets/lv_bar.h
.\_build\power.o: ..\lvgl\src/widgets/lv_slider.h
.\_build\power.o: ..\lvgl\src/widgets/lv_btnmatrix.h
.\_build\power.o: ..\lvgl\src/widgets/lv_dropdown.h
.\_build\power.o: ..\lvgl\src/widgets/lv_roller.h
.\_build\power.o: ..\lvgl\src/widgets/lv_textarea.h
.\_build\power.o: ..\lvgl\src/widgets/lv_canvas.h
.\_build\power.o: ..\lvgl\src/widgets/lv_switch.h
.\_build\power.o: ..\lvgl\src/lv_api_map.h
.\_build\power.o: ..\lvgl\src/../lvgl.h
.\_build\power.o: ..\lvgl\src/extra/lv_extra.h
.\_build\power.o: ..\lvgl\src/extra/widgets/lv_widgets.h
.\_build\power.o: ..\lvgl\src/extra/widgets/animimg/lv_animimg.h
.\_build\power.o: ..\lvgl\src/extra/widgets/animimg/../../../lvgl.h
.\_build\power.o: ..\lvgl\src/extra/widgets/calendar/lv_calendar.h
.\_build\power.o: ..\lvgl\src/extra/widgets/calendar/lv_calendar_header_arrow.h
.\_build\power.o: ..\lvgl\src/extra/widgets/calendar/lv_calendar_header_dropdown.h
.\_build\power.o: ..\lvgl\src/extra/widgets/chart/lv_chart.h
.\_build\power.o: ..\lvgl\src/extra/widgets/keyboard/lv_keyboard.h
.\_build\power.o: ..\lvgl\src/extra/widgets/list/lv_list.h
.\_build\power.o: ..\lvgl\src/extra/widgets/list/../../layouts/flex/lv_flex.h
.\_build\power.o: ..\lvgl\src/extra/widgets/menu/lv_menu.h
.\_build\power.o: ..\lvgl\src/extra/widgets/msgbox/lv_msgbox.h
.\_build\power.o: ..\lvgl\src/extra/widgets/meter/lv_meter.h
.\_build\power.o: ..\lvgl\src/extra/widgets/spinbox/lv_spinbox.h
.\_build\power.o: ..\lvgl\src/extra/widgets/spinner/lv_spinner.h
.\_build\power.o: ..\lvgl\src/extra/widgets/tabview/lv_tabview.h
.\_build\power.o: ..\lvgl\src/extra/widgets/tileview/lv_tileview.h
.\_build\power.o: ..\lvgl\src/extra/widgets/win/lv_win.h
.\_build\power.o: ..\lvgl\src/extra/widgets/colorwheel/lv_colorwheel.h
.\_build\power.o: ..\lvgl\src/extra/widgets/led/lv_led.h
.\_build\power.o: ..\lvgl\src/extra/widgets/imgbtn/lv_imgbtn.h
.\_build\power.o: ..\lvgl\src/extra/widgets/span/lv_span.h
.\_build\power.o: ..\lvgl\src/extra/layouts/lv_layouts.h
.\_build\power.o: ..\lvgl\src/extra/layouts/grid/lv_grid.h
.\_build\power.o: ..\lvgl\src/extra/themes/lv_themes.h
.\_build\power.o: ..\lvgl\src/extra/themes/default/lv_theme_default.h
.\_build\power.o: ..\lvgl\src/extra/themes/mono/lv_theme_mono.h
.\_build\power.o: ..\lvgl\src/extra/themes/basic/lv_theme_basic.h
.\_build\power.o: ..\lvgl\src/extra/others/lv_others.h
.\_build\power.o: ..\lvgl\src/extra/others/snapshot/lv_snapshot.h
.\_build\power.o: ..\lvgl\src/extra/others/monkey/lv_monkey.h
.\_build\power.o: ..\lvgl\src/extra/others/gridnav/lv_gridnav.h
.\_build\power.o: ..\lvgl\src/extra/libs/lv_libs.h
.\_build\power.o: ..\lvgl\src/extra/libs/bmp/lv_bmp.h
.\_build\power.o: ..\lvgl\src/extra/libs/fsdrv/lv_fsdrv.h
.\_build\power.o: ..\lvgl\src/extra/libs/png/lv_png.h
.\_build\power.o: ..\lvgl\src/extra/libs/gif/lv_gif.h
.\_build\power.o: ..\lvgl\src/extra/libs/gif/gifdec.h
.\_build\power.o: ..\lvgl\src/extra/libs/qrcode/lv_qrcode.h
.\_build\power.o: ..\lvgl\src/extra/libs/sjpg/lv_sjpg.h
.\_build\power.o: ..\lvgl\src/extra/libs/freetype/lv_freetype.h
.\_build\power.o: ..\lvgl\src/extra/libs/rlottie/lv_rlottie.h
.\_build\power.o: ..\lvgl\src/extra/libs/ffmpeg/lv_ffmpeg.h
